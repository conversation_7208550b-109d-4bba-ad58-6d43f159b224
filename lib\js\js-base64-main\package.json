{"name": "js-base64", "version": "3.7.5", "description": "Yet another Base64 transcoder in pure-JS", "main": "base64.js", "module": "base64.mjs", "types": "base64.d.ts", "sideEffects": false, "files": ["base64.js", "base64.mjs", "base64.d.ts"], "exports": {".": {"types": "./base64.d.ts", "import": "./base64.mjs", "require": "./base64.js"}, "./package.json": "./package.json"}, "scripts": {"test": "make clean && make test"}, "devDependencies": {"@types/node": "^18.15.11", "mocha": "^10.0.0", "typescript": "^5.0.0"}, "repository": "git+https://github.com/dankogai/js-base64.git", "keywords": ["base64", "binary"], "author": "<PERSON>", "license": "BSD-3-<PERSON><PERSON>"}